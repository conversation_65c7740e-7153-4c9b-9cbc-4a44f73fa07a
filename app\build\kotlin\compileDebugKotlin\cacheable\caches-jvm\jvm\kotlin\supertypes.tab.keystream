com.airdoc.mpd.MainActivitycom.airdoc.mpd.MenuPopupWindow#com.airdoc.mpd.MoreSettingsActivitycom.airdoc.mpd.MpdApplication&com.airdoc.mpd.camera.CameraXViewModel)com.airdoc.mpd.common.CommonLoadingDialog&com.airdoc.mpd.common.CommonPreference(com.airdoc.mpd.common.MultiClickListener'com.airdoc.mpd.common.language.Language.com.airdoc.mpd.common.language.LanguageAdapter=com.airdoc.mpd.common.language.LanguageAdapter.LanguageHolder5com.airdoc.mpd.common.language.LanguageSettingsDialog$com.airdoc.mpd.config.ConfigActivity*com.airdoc.mpd.detection.DetectionActivity+com.airdoc.mpd.detection.DetectionActivity10com.airdoc.mpd.detection.DetectionProjectAdapterGcom.airdoc.mpd.detection.DetectionProjectAdapter.DetectionProjectHolder-com.airdoc.mpd.detection.DetectionWebActivity)com.airdoc.mpd.detection.DetectionWebView(com.airdoc.mpd.detection.hrv.HrvActivity'com.airdoc.mpd.detection.hrv.HrvWebView8com.airdoc.mpd.detection.hrv.HrvWebView.HrvWebViewClient:com.airdoc.mpd.detection.hrv.HrvWebView.HrvWebChromeClient&com.airdoc.mpd.device.DeviceInfoDialog/com.airdoc.mpd.device.StartupModeSettingsDialog%com.airdoc.mpd.device.bean.DeviceInfo$com.airdoc.mpd.device.bean.FetchInfo&com.airdoc.mpd.device.bean.Instruction!com.airdoc.mpd.device.bean.QRCode-com.airdoc.mpd.device.enumeration.BillingMode-com.airdoc.mpd.device.enumeration.StartupMode1com.airdoc.mpd.device.repository.DeviceRepository(com.airdoc.mpd.device.vm.DeviceViewModel'com.airdoc.mpd.gaze.GazeTrackingManager.com.airdoc.mpd.gaze.application.AppliedManager,com.airdoc.mpd.gaze.bean.CalibrateCoordinate*com.airdoc.mpd.gaze.bean.CalibrationResult(com.airdoc.mpd.gaze.bean.GazeTrackResult1com.airdoc.mpd.gaze.bean.PostureCalibrationResult3com.airdoc.mpd.gaze.calibration.CalibrationActivityGcom.airdoc.mpd.gaze.calibration.CalibrationActivity.CalibrationWSClient+com.airdoc.mpd.gaze.enumeration.AppliedMode/com.airdoc.mpd.gaze.enumeration.CalibrationMode,com.airdoc.mpd.gaze.enumeration.CoverChannel)com.airdoc.mpd.gaze.enumeration.CoverMode*com.airdoc.mpd.gaze.enumeration.CoverRange+com.airdoc.mpd.gaze.enumeration.ServiceMode/com.airdoc.mpd.gaze.repository.ReportRepository*com.airdoc.mpd.gaze.track.GazeTrackService.com.airdoc.mpd.gaze.track.GazeWebSocketService)com.airdoc.mpd.gaze.track.TrackingManager+com.airdoc.mpd.gaze.vm.CalibrationViewModel"com.airdoc.mpd.gaze.widget.DotView1com.airdoc.mpd.gaze.widget.PostureCalibrationView0com.airdoc.mpd.gaze.widget.VisualCalibrationView2com.airdoc.mpd.home.DetectionCodeDetectionFragment+com.airdoc.mpd.home.DeviceExceptionFragment.com.airdoc.mpd.home.InputInfoDetectionFragment-com.airdoc.mpd.home.ScanCodeDetectionFragment7com.airdoc.mpd.media.PlayManager.ExternalPlayerListener%com.airdoc.mpd.media.bean.AssetsMedia"com.airdoc.mpd.media.bean.RawMedia%com.airdoc.mpd.media.bean.StreamMedia"com.airdoc.mpd.media.bean.UrlMedia4com.airdoc.mpd.media.factory.StreamDataSourceFactory9com.airdoc.mpd.media.player.ExoMediaPlayer.PlayerListener)com.airdoc.mpd.media.player.PlaybackState,com.airdoc.mpd.media.source.StreamDataSource3com.airdoc.mpd.medicalhome.enumeration.AmblyopicEye.com.airdoc.mpd.medicalhome.mask.MaskPreference*com.airdoc.mpd.net.CommonParamsInterceptor$com.airdoc.mpd.net.MpdRetrofitClient&com.airdoc.mpd.ppg.bean.AnalysisResult"com.airdoc.mpd.ppg.bean.BandPowers&com.airdoc.mpd.ppg.bean.BandStatistics1com.airdoc.mpd.ppg.bean.FrequencyDomainParameters$com.airdoc.mpd.ppg.bean.PPGDataPoint,com.airdoc.mpd.ppg.bean.TimeDomainParameters"com.airdoc.mpd.ppg.vm.PpgViewModel'com.airdoc.mpd.provider.MpdFileProvider com.airdoc.mpd.scan.ScanActivity$com.airdoc.mpd.update.UpdateActivity"com.airdoc.mpd.update.UpdateDialog(com.airdoc.mpd.update.bean.AppUpdateInfo%com.airdoc.mpd.update.bean.AppVersion1com.airdoc.mpd.update.repository.UpdateRepository(com.airdoc.mpd.update.vm.UpdateViewModel&com.airdoc.mpd.user.SelectionAgeDialog)com.airdoc.mpd.user.bean.DetectionProject*com.airdoc.mpd.user.bean.DetectionProjectscom.airdoc.mpd.user.bean.User&com.airdoc.mpd.user.enumeration.Gender-com.airdoc.mpd.user.repository.UserRepository$com.airdoc.mpd.user.vm.UserViewModel.com.airdoc.mpd.databinding.ActivityMainBinding6com.airdoc.mpd.databinding.ActivityMoreSettingsBinding7com.airdoc.mpd.databinding.LayoutMenuPopupWindowBinding0com.airdoc.mpd.ppg.websocket.PpgWebSocketService*com.airdoc.mpd.device.DeviceReminderDialog(com.airdoc.mpd.device.DeviceReminderType6com.airdoc.mpd.databinding.DialogDeviceReminderBinding0com.airdoc.mpd.detection.bean.MaterialLibraryDto6com.airdoc.mpd.detection.repository.MaterialRepository-com.airdoc.mpd.detection.vm.MaterialViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       