package com.airdoc.mpd.detection.hrv

import android.content.Context
import android.util.AttributeSet
import android.webkit.JavascriptInterface
import android.webkit.PermissionRequest
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.CustomWebView
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.common.language.LanguageManager
import com.airdoc.mpd.detection.MaterialHelper
import com.airdoc.mpd.device.DeviceManager
import com.google.gson.Gson
import org.json.JSONArray
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.net.MalformedURLException
import java.net.URL
import android.util.Base64
import java.io.FileInputStream

/**
 *FileName: HrvWebView
 * Author by lilin,Date on 2025/4/9 16:29
 * PS: Not easy to write code, please indicate.
 */
class HrvWebView : CustomWebView {

    companion object{
        private val TAG = HrvWebView::class.java.simpleName
    }

    constructor(context: Context) : super(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    init {
        // 启用混合内容（HTTP和HTTPS混合）
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        // 设置缓存模式 - 启用缓存
        settings.cacheMode = WebSettings.LOAD_DEFAULT
//        settings.cacheMode = WebSettings.LOAD_NO_CACHE
        // 启用DOM存储
        settings.domStorageEnabled = true
        // 启用数据库存储
        settings.databaseEnabled = true
        // 启用JavaScript（确保JavaScript可以正常工作）
        settings.javaScriptEnabled = true
        // 设置用户代理字符串
        settings.userAgentString = settings.userAgentString + " HrvWebView/1.0"
        // 启用文件访问
        settings.allowFileAccess = true
        // 启用内容访问
        settings.allowContentAccess = true


        Logger.d(TAG, msg = "HrvWebView - 配置已启用")
        Logger.d(TAG, msg = "  缓存模式: LOAD_DEFAULT")
        Logger.d(TAG, msg = "  DOM存储: 已启用")
        Logger.d(TAG, msg = "  数据库存储: 已启用")
        Logger.d(TAG, msg = "  JavaScript: 已启用")
        Logger.d(TAG, msg = "  文件访问: 已启用")
        Logger.d(TAG, msg = "  内容访问: 已启用")
        Logger.d(TAG, msg = "  混合内容: 已允许")
        Logger.d(TAG, msg = "  缓存路径: ${context.cacheDir.absolutePath}")

        setWebViewClient(HrvWebViewClient())
        setWebChromeClient(HrvWebChromeClient())
    }

    private var hrvActionListener: HrvActionListener? = null

    fun setHrvActionListener(listener: HrvActionListener?){
        hrvActionListener = listener
    }

    /**
     * 清除WebView缓存
     */
    fun clearWebViewCache() {
        try {
            Logger.d(TAG, msg = "开始清除HrvWebView缓存...")

            // 清除缓存（包括磁盘缓存和内存缓存）
            clearCache(true)

            // 清除历史记录
            clearHistory()

            // 清除表单数据
            clearFormData()

            // 清除SSL偏好设置
            clearSslPreferences()

            // 清除WebView存储（localStorage, sessionStorage等）
            clearMatches()

            Logger.d(TAG, msg = "HrvWebView缓存清除完成")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "清除HrvWebView缓存异常: ${e.message}")
        }
    }

    interface HrvActionListener{
        fun onPageFinished()
        fun onFinish()
        fun goHome()
        fun onPrintPage()
        fun onReady()
        fun onStartGazeTracking()
        fun onStopGazeTracking()
        fun onGazeTrackingStatus(isEnabled: Boolean)
        fun generatePpgAnalysisReport(): String?
    }

    inner class HrvAction{

        @JavascriptInterface
        fun onFinish() {
            try {
                Logger.d(TAG, msg = "onFinish")
                post {
                    hrvActionListener?.onFinish()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "onFinish error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun goHome() {
            try {
                Logger.d(TAG, msg = "goHome")
                post {
                    hrvActionListener?.goHome()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "goHome error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun printPage() {
            try {
                Logger.d(TAG, msg = "printPage")
                post {
                    hrvActionListener?.onPrintPage()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "printPage error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun ready(){
            try {
                Logger.d(TAG, msg = "ready")
                post {
                    hrvActionListener?.onReady()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "ready error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun startGazeTracking() {
            try {
                Logger.d(TAG, msg = "🚀 JavaScript调用: startGazeTracking - 启动眼动追踪")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")
                post {
                    hrvActionListener?.onStartGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "startGazeTracking error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun stopGazeTracking() {
            try {
                Logger.d(TAG, msg = "🛑 JavaScript调用: stopGazeTracking - 停止眼动追踪")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")
                post {
                    hrvActionListener?.onStopGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "stopGazeTracking error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun setGazeTrackingEnabled(enabled: Boolean) {
            try {
                Logger.d(TAG, msg = "⚙️ JavaScript调用: setGazeTrackingEnabled - 设置眼动追踪状态")
                Logger.d(TAG, msg = "  启用状态: $enabled")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")
                post {
                    hrvActionListener?.onGazeTrackingStatus(enabled)
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "setGazeTrackingEnabled error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun getInitialParams(): String {
            try {
                Logger.d(TAG, msg = "📋 JavaScript调用: getInitialParams - 获取初始参数")

                // 获取当前语言
                val userLanguage = LanguageManager.getUserLanguage(context)
                val languageCode = "${userLanguage.language}-${userLanguage.country}"

                // 获取设备SN
                val deviceSn = DeviceManager.getDeviceSn()

                // 检查指尖式数据采集开关是否开启
                val isFingertipCollectionEnabled = MMKVManager.decodeBool(CommonPreference.ENABLE_FINGERTIP_DATA_COLLECTION) ?: true

                // 只有在指尖式采集器开关打开后才返回采集器编号
                val collectorSn = if (isFingertipCollectionEnabled) {
                    MMKVManager.decodeString(CommonPreference.COLLECTOR_NUMBER)
                } else {
                    null
                }

                // 构建JSON对象
                val params = JSONObject().apply {
                    put("language", languageCode)
                    put("sn", deviceSn)
                    if (collectorSn != null) {
                        put("collectorSn", collectorSn)
                    }
                    put("timestamp", System.currentTimeMillis())
                }

                val result = params.toString()
                Logger.d(TAG, msg = "  返回参数: $result")
                Logger.d(TAG, msg = "  语言: $languageCode")
                Logger.d(TAG, msg = "  设备SN: $deviceSn")
                Logger.d(TAG, msg = "  指尖式采集开关: $isFingertipCollectionEnabled")
                if (collectorSn != null) {
                    Logger.d(TAG, msg = "  采集器编号: $collectorSn")
                } else {
                    Logger.d(TAG, msg = "  采集器编号: 未返回（开关关闭）")
                }

                return result
            } catch (e: Exception) {
                Logger.e(TAG, msg = "getInitialParams error: ${e.message}")
                // 返回默认参数
                val defaultParams = JSONObject().apply {
                    put("language", "zh-CN")
                    put("sn", "unknown")
                    put("collectorSn", "unknown")
                    put("timestamp", System.currentTimeMillis())
                    put("error", e.message)
                }
                return defaultParams.toString()
            }
        }

        @JavascriptInterface
        fun generatePpgReport(): String {
            try {
                Logger.d(TAG, msg = "📊 JavaScript调用: generatePpgReport - 生成PPG分析报告")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")

                val reportJson = hrvActionListener?.generatePpgAnalysisReport()

                if (reportJson != null) {
                    Logger.d(TAG, msg = "  报告生成成功，长度: ${reportJson.length}")
                    return reportJson
                } else {
                    Logger.w(TAG, msg = "  报告生成失败，返回空结果")
                    val errorResult = JSONObject().apply {
                        put("success", false)
                        put("error", "无法生成PPG分析报告")
                        put("timestamp", System.currentTimeMillis())
                    }
                    return errorResult.toString()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "generatePpgReport error: ${e.message}")
                val errorResult = JSONObject().apply {
                    put("success", false)
                    put("error", e.message)
                    put("timestamp", System.currentTimeMillis())
                }
                return errorResult.toString()
            }
        }

        @JavascriptInterface
        fun saveAssessmentResult(resultJson: String) {
            try {
                Logger.d(TAG, msg = "💾 JavaScript调用: saveAssessmentResult - 保存评估结果")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  数据长度: ${resultJson.length}")

                // 验证JSON格式
                val jsonObject = try {
                    JSONObject(resultJson)
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "  JSON格式验证失败: ${e.message}")
                    return
                }

                // 添加时间戳
                jsonObject.put("savedTimestamp", System.currentTimeMillis())
                val finalJson = jsonObject.toString()

                // 保存到MMKV
                MMKVManager.encodeString(CommonPreference.HRV_ASSESSMENT_RESULT, finalJson)

                Logger.d(TAG, msg = "  评估结果保存成功")
                Logger.d(TAG, msg = "  保存的数据: ${finalJson.take(200)}${if (finalJson.length > 200) "..." else ""}")

            } catch (e: Exception) {
                Logger.e(TAG, msg = "saveAssessmentResult error: ${e.message}")
            }
        }

        /**
         * 获取批量图片URL列表（混合模式 - 推荐方案）
         * 返回图片URL列表，配合shouldInterceptRequest实现高性能批量加载
         */
        @JavascriptInterface
        fun getBatchImageUrls(imageIdsJson: String): String {
            try {
                Logger.d(TAG, msg = "🔗 JavaScript调用: getBatchImageUrls - 获取批量图片URL")
                Logger.d(TAG, msg = "  请求参数: $imageIdsJson")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")

                // 解析图片ID列表
                val imageIds = try {
                    val jsonArray = JSONArray(imageIdsJson)
                    val idList = mutableListOf<String>()
                    for (i in 0 until jsonArray.length()) {
                        idList.add(jsonArray.getString(i))
                    }
                    idList
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "  解析图片ID列表失败: ${e.message}")
                    return createErrorResponse("Invalid imageIds JSON format")
                }

                // 验证图片是否存在并生成URL列表
                val imageUrls = mutableListOf<Map<String, Any>>()
                imageIds.forEach { imageId ->
                    val imageInfo = getImageInfoForUrl(imageId)
                    if (imageInfo != null) {
                        imageUrls.add(imageInfo)
                    }
                }

                val resultJson = JSONObject()
                resultJson.put("success", true)
                resultJson.put("count", imageUrls.size)
                resultJson.put("urls", JSONArray(imageUrls))
                resultJson.put("timestamp", System.currentTimeMillis())
                resultJson.put("method", "webview_intercept")

                val result = resultJson.toString()
                Logger.d(TAG, msg = "  URL列表生成成功，返回${imageUrls.size}个图片URL")
                return result
            } catch (e: Exception) {
                Logger.e(TAG, msg = "getBatchImageUrls error: ${e.message}")
                return createErrorResponse("Exception: ${e.message}")
            }
        }

        /**
         * 创建错误响应JSON
         */
        private fun createErrorResponse(errorMessage: String): String {
            val errorJson = JSONObject()
            errorJson.put("success", false)
            errorJson.put("error", errorMessage)
            errorJson.put("timestamp", System.currentTimeMillis())
            return errorJson.toString()
        }

        /**
         * 获取图片信息用于生成URL
         */
        private fun getImageInfoForUrl(imageId: String): Map<String, Any>? {
            return try {
                // 检查图片是否存在
                val exists = when {
                    imageId.startsWith("camera") -> {
                        hrvActionListener?.onRequestCameraImage() != null
                    }
                    imageId.startsWith("material_") -> {
                        val materialPath = imageId.removePrefix("material_")
                        val filePath = "material://$materialPath"
                        hrvActionListener?.onRequestImageFile(filePath)?.exists() == true
                    }
                    else -> {
                        hrvActionListener?.onRequestImageFile(imageId)?.exists() == true
                    }
                }

                if (exists) {
                    // 生成拦截URL
                    val interceptUrl = when {
                        imageId.startsWith("camera") -> "/camera/$imageId"
                        imageId.startsWith("material_") -> "/material/${imageId.removePrefix("material_")}"
                        imageId.startsWith("temp_") -> "/temp/${imageId.removePrefix("temp_")}"
                        else -> "/images/$imageId"
                    }

                    mapOf(
                        "id" to imageId,
                        "url" to interceptUrl,
                        "intercepted" to true,
                        "timestamp" to System.currentTimeMillis()
                    )
                } else {
                    Logger.w(TAG, msg = "  图片不存在: $imageId")
                    null
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "  获取图片信息失败: $imageId - ${e.message}")
                null
            }
        }

    }

    inner class HrvWebViewClient : CustomWebViewClient(){
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            hrvActionListener?.onPageFinished()
        }

        override fun shouldInterceptRequest(
            view: WebView?,
            request: WebResourceRequest?
        ): WebResourceResponse? {
            val url = request?.url.toString()
            val startTime = System.currentTimeMillis()

            try {
                Logger.d(TAG, msg = "🔍 拦截请求: $url")

                // 拦截配置文件资源 (opencv.js, face_landmarker.task等)
                if (shouldInterceptConfigResource(url)) {
                    return interceptConfigResource(url, startTime)
                }

                // 拦截图片资源 (核心功能)
                if (shouldInterceptImageResource(url)) {
                    return interceptImageResource(url, startTime)
                }

            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Logger.e(TAG, msg = "❌ 资源拦截异常: $url (${duration}ms) - ${e.message}")
                e.printStackTrace()
            }

            // 不需要拦截的资源返回null，让WebView自己处理
            return null
        }

        /**
         * 拦截配置文件资源
         */
        private fun interceptConfigResource(url: String, startTime: Long): WebResourceResponse? {
            return try {
                val fileName: String = getFileNameFromUrl(url)
                Logger.d(TAG, msg = "📁 拦截配置资源: $fileName")

                val inputStream = context.assets.open("configs/$fileName")
                val response = WebResourceResponse(getMimeType(fileName), "UTF-8", inputStream)

                // 添加缓存头
                response.responseHeaders = mapOf(
                    "Cache-Control" to "max-age=3600",
                    "Access-Control-Allow-Origin" to "*"
                )

                val duration = System.currentTimeMillis() - startTime
                Logger.d(TAG, msg = "✅ 配置资源加载完成: $fileName (${duration}ms)")
                response
            } catch (e: IOException) {
                val duration = System.currentTimeMillis() - startTime
                Logger.e(TAG, msg = "❌ 配置资源加载失败: $url (${duration}ms) - ${e.message}")
                null
            }
        }

        /**
         * 拦截图片资源的核心实现
         */
        private fun interceptImageResource(url: String, startTime: Long): WebResourceResponse? {
            return try {
                Logger.d(TAG, msg = "🖼️ 拦截图片资源: $url")

                // 解析图片ID或路径
                val imageId = extractImageIdFromUrl(url)
                Logger.d(TAG, msg = "  解析图片ID: $imageId")

                // 获取图片数据
                val imageData = getImageDataForIntercept(imageId)

                if (imageData != null) {
                    val response = WebResourceResponse(
                        imageData.mimeType,
                        null,
                        imageData.inputStream
                    )

                    // 添加优化的缓存头
                    response.responseHeaders = mapOf(
                        "Cache-Control" to "max-age=86400", // 24小时缓存
                        "Last-Modified" to formatHttpDate(imageData.lastModified),
                        "Content-Length" to imageData.contentLength.toString(),
                        "Access-Control-Allow-Origin" to "*",
                        "Content-Type" to imageData.mimeType
                    )

                    val duration = System.currentTimeMillis() - startTime
                    Logger.d(TAG, msg = "✅ 图片资源拦截成功: $imageId (${duration}ms, ${imageData.contentLength}bytes)")
                    response
                } else {
                    val duration = System.currentTimeMillis() - startTime
                    Logger.w(TAG, msg = "⚠️ 图片资源未找到: $imageId (${duration}ms)")
                    null
                }
            } catch (e: Exception) {
                val duration = System.currentTimeMillis() - startTime
                Logger.e(TAG, msg = "❌ 图片资源拦截异常: $url (${duration}ms) - ${e.message}")
                null
            }
        }
    }

    private fun getMimeType(fileName: String): String {
        // 根据文件扩展名返回MIME类型
        if (fileName.endsWith(".js")) {
            return "application/javascript"
        } else if (fileName.endsWith(".css")) {
            return "text/css"
        } else if (fileName.endsWith(".png")) {
            return "image/png"
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg"
        } else if (fileName.endsWith(".woff2")) {
            return "font/woff2"
        } else if (fileName.endsWith(".task")) {
            return "application/octet-stream"
        }
        return "text/plain"
    }

    private fun shouldInterceptLocalResource(url: String): Boolean {
        // 定义需要本地加载的资源类型和路径
        val localPaths: List<String> = mutableListOf(
            "/opencv",
            "/mediapipe"
        )

        for (path in localPaths) {
            if (url.contains(path)) {
                return true
            }
        }
        return false
    }

    private fun shouldInterceptConfigResource(url: String): Boolean {
        // 定义需要从configs目录加载的资源
        val configFiles: List<String> = mutableListOf(
            "opencv.js",
            "face_landmarker.task"
        )

        for (fileName in configFiles) {
            if (url.contains(fileName)) {
                return true
            }
        }
        return false
    }

    /**
     * 检查是否应该拦截图片资源
     */
    private fun shouldInterceptImageResource(url: String): Boolean {
        // 定义需要拦截的图片资源模式
        val imagePatterns = listOf(
            "/images/",           // 通用图片路径
            "/assets/images/",    // Assets图片路径
            "/material/",         // 素材库图片路径
            "/camera/",           // 相机图片路径
            "/temp/",             // 临时图片路径
            "android-image://",   // 自定义协议
            "file:///android_asset/images/", // Asset文件协议
            "content://images/"   // Content Provider协议
        )

        // 检查URL是否匹配图片模式
        val matchesPattern = imagePatterns.any { pattern -> url.contains(pattern) }

        // 检查文件扩展名
        val imageExtensions = listOf(".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp")
        val hasImageExtension = imageExtensions.any { ext -> url.lowercase().contains(ext) }

        val shouldIntercept = matchesPattern || hasImageExtension

        if (shouldIntercept) {
            Logger.d(TAG, msg = "🎯 图片资源匹配拦截规则: $url")
        }

        return shouldIntercept
    }

    private fun getFileNameFromUrl(url: String): String {
        try {
            // 解析URL
            val urlObj = URL(url)
            // 获取路径部分
            var path = urlObj.path

            // 移除开头的斜杠(如果存在)
            if (path.startsWith("/")) {
                path = path.substring(1)
            }

            return path
        } catch (e: MalformedURLException) {
            // URL解析失败的情况下，使用简单的字符串处理
            val parts = url.split("//".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (parts.size > 1) {
                // 取host之后的部分
                val index = parts[1].indexOf("/")
                if (index != -1) {
                    return parts[1].substring(index + 1)
                }
            }
            return url
        }
    }

    /**
     * 图片数据封装类
     */
    data class ImageInterceptData(
        val inputStream: InputStream,
        val mimeType: String,
        val contentLength: Long,
        val lastModified: Long = System.currentTimeMillis()
    )

    /**
     * 从URL中提取图片ID
     */
    private fun extractImageIdFromUrl(url: String): String {
        return when {
            url.contains("/images/") -> {
                url.substringAfterLast("/images/").substringBefore("?").substringBefore("#")
            }
            url.contains("/material/") -> {
                "material_" + url.substringAfterLast("/material/").substringBefore("?").substringBefore("#")
            }
            url.contains("/camera/") -> {
                "camera_" + url.substringAfterLast("/camera/").substringBefore("?").substringBefore("#")
            }
            url.contains("/temp/") -> {
                "temp_" + url.substringAfterLast("/temp/").substringBefore("?").substringBefore("#")
            }
            url.contains("android-image://") -> {
                url.removePrefix("android-image://").substringBefore("?").substringBefore("#")
            }
            url.contains("content://images/") -> {
                "content_" + url.substringAfterLast("/").substringBefore("?").substringBefore("#")
            }
            else -> {
                // 默认提取文件名
                url.substringAfterLast("/").substringBefore("?").substringBefore("#")
            }
        }
    }

    /**
     * 获取用于拦截的图片数据
     */
    private fun getImageDataForIntercept(imageId: String): ImageInterceptData? {
        return try {
            Logger.d(TAG, msg = "🔍 获取图片数据: $imageId")

            when {
                // 素材库图片
                imageId.startsWith("material_") -> {
                    Logger.d(TAG, msg = "  处理素材库图片: $imageId")
                    val materialPath = imageId.removePrefix("material_")
                    val filePath = "material://$materialPath"
                    val file = hrvActionListener?.onRequestImageFile(filePath)
                    file?.let {
                        Logger.d(TAG, msg = "  素材库图片文件找到: ${it.absolutePath}")
                        createImageDataFromFile(it)
                    }
                }
                // 默认处理：尝试作为文件路径
                else -> {
                    Logger.d(TAG, msg = "  默认处理图片: $imageId")
                    val file = hrvActionListener?.onRequestImageFile(imageId)
                    file?.let {
                        Logger.d(TAG, msg = "  默认图片文件找到: ${it.absolutePath}")
                        createImageDataFromFile(it)
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "❌ 获取拦截图片数据失败: $imageId - ${e.message}")
            null
        }
    }


    /**
     * 从文件创建图片数据
     */
    private fun createImageDataFromFile(file: File): ImageInterceptData {
        Logger.d(TAG, msg = "  文件信息: ${file.name}, 大小: ${file.length()}bytes")

        return ImageInterceptData(
            inputStream = FileInputStream(file),
            mimeType = getMimeTypeFromFile(file),
            contentLength = file.length(),
            lastModified = file.lastModified()
        )
    }

    /**
     * 根据文件获取MIME类型
     */
    private fun getMimeTypeFromFile(file: File): String {
        val fileName = file.name.lowercase()
        return when {
            fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") -> "image/jpeg"
            fileName.endsWith(".png") -> "image/png"
            fileName.endsWith(".gif") -> "image/gif"
            fileName.endsWith(".webp") -> "image/webp"
            fileName.endsWith(".bmp") -> "image/bmp"
            fileName.endsWith(".svg") -> "image/svg+xml"
            else -> "image/jpeg" // 默认
        }
    }

    /**
     * 格式化HTTP日期
     */
    private fun formatHttpDate(timestamp: Long): String {
        val sdf = java.text.SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz", java.util.Locale.US)
        sdf.timeZone = java.util.TimeZone.getTimeZone("GMT")
        return sdf.format(java.util.Date(timestamp))
    }

    inner class HrvWebChromeClient : CustomWebChromeClient(){

        override fun onPermissionRequest(request: PermissionRequest?) {
            Logger.d(TAG, msg = "onPermissionRequest")
            request?.grant(request.resources)
        }
    }

}