package com.airdoc.mpd.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.airdoc.component.common.log.Logger
import fi.iki.elonen.NanoHTTPD
import java.io.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 本地图片HTTP服务器
 * 提供高性能的图片访问服务，避免Base64编码开销
 */
class LocalImageServer(private val context: Context, port: Int = 8080) : NanoHTTPD(port) {
    
    companion object {
        private val TAG = LocalImageServer::class.java.simpleName
        private const val CACHE_CONTROL_MAX_AGE = 3600 // 1小时缓存
    }
    
    // 图片缓存映射：URL路径 -> 图片数据
    private val imageCache = ConcurrentHashMap<String, ImageData>()
    
    // 临时图片存储：用于动态生成的图片
    private val tempImages = ConcurrentHashMap<String, Bitmap>()
    
    data class ImageData(
        val bitmap: Bitmap?,
        val filePath: String?,
        val mimeType: String = "image/jpeg",
        val lastModified: Long = System.currentTimeMillis()
    )
    
    override fun serve(session: IHTTPSession): Response {
        val uri = session.uri
        Logger.d(TAG, msg = "📡 HTTP请求: $uri")
        
        return when {
            uri.startsWith("/image/") -> serveImage(uri)
            uri.startsWith("/batch/") -> serveBatchImages(uri, session)
            uri == "/health" -> serveHealthCheck()
            else -> newFixedLengthResponse(Response.Status.NOT_FOUND, "text/plain", "Not Found")
        }
    }
    
    /**
     * 服务单张图片
     * URL格式: /image/{imageId}
     */
    private fun serveImage(uri: String): Response {
        return try {
            val imageId = uri.removePrefix("/image/")
            val imageData = getImageData(imageId)
            
            if (imageData != null) {
                val inputStream = when {
                    imageData.bitmap != null -> bitmapToInputStream(imageData.bitmap)
                    imageData.filePath != null -> FileInputStream(imageData.filePath)
                    else -> null
                }
                
                if (inputStream != null) {
                    val response = newChunkedResponse(Response.Status.OK, imageData.mimeType, inputStream)
                    response.addHeader("Cache-Control", "max-age=$CACHE_CONTROL_MAX_AGE")
                    response.addHeader("Last-Modified", formatHttpDate(imageData.lastModified))
                    Logger.d(TAG, msg = "✅ 成功服务图片: $imageId")
                    response
                } else {
                    Logger.w(TAG, msg = "⚠️ 图片数据为空: $imageId")
                    newFixedLengthResponse(Response.Status.NOT_FOUND, "text/plain", "Image data not found")
                }
            } else {
                Logger.w(TAG, msg = "⚠️ 图片不存在: $imageId")
                newFixedLengthResponse(Response.Status.NOT_FOUND, "text/plain", "Image not found")
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "❌ 服务图片异常: ${e.message}")
            newFixedLengthResponse(Response.Status.INTERNAL_ERROR, "text/plain", "Server error")
        }
    }
    
    /**
     * 服务批量图片信息
     * URL格式: /batch/{imageIds} 或 POST /batch/ with JSON body
     */
    private fun serveBatchImages(uri: String, session: IHTTPSession): Response {
        return try {
            val imageIds = if (session.method == Method.POST) {
                // 从POST body获取图片ID列表
                val bodySize = session.headers["content-length"]?.toIntOrNull() ?: 0
                val body = ByteArray(bodySize)
                session.inputStream.read(body)
                val jsonBody = String(body)
                parseImageIdsFromJson(jsonBody)
            } else {
                // 从URL路径获取图片ID列表
                val idsParam = uri.removePrefix("/batch/")
                idsParam.split(",").filter { it.isNotEmpty() }
            }
            
            val imageInfoList = imageIds.mapNotNull { imageId ->
                val imageData = getImageData(imageId)
                if (imageData != null) {
                    mapOf(
                        "id" to imageId,
                        "url" to "http://localhost:$listeningPort/image/$imageId",
                        "width" to (imageData.bitmap?.width ?: 0),
                        "height" to (imageData.bitmap?.height ?: 0),
                        "mimeType" to imageData.mimeType,
                        "lastModified" to imageData.lastModified
                    )
                } else null
            }
            
            val responseJson = mapOf(
                "success" to true,
                "count" to imageInfoList.size,
                "images" to imageInfoList,
                "timestamp" to System.currentTimeMillis()
            )
            
            val jsonResponse = com.google.gson.Gson().toJson(responseJson)
            val response = newFixedLengthResponse(Response.Status.OK, "application/json", jsonResponse)
            response.addHeader("Access-Control-Allow-Origin", "*")
            Logger.d(TAG, msg = "✅ 成功服务批量图片信息: ${imageInfoList.size}张")
            response
        } catch (e: Exception) {
            Logger.e(TAG, msg = "❌ 服务批量图片异常: ${e.message}")
            val errorJson = mapOf(
                "success" to false,
                "error" to e.message,
                "timestamp" to System.currentTimeMillis()
            )
            newFixedLengthResponse(Response.Status.INTERNAL_ERROR, "application/json", 
                com.google.gson.Gson().toJson(errorJson))
        }
    }
    
    /**
     * 健康检查接口
     */
    private fun serveHealthCheck(): Response {
        val healthInfo = mapOf(
            "status" to "ok",
            "port" to listeningPort,
            "cachedImages" to imageCache.size,
            "tempImages" to tempImages.size,
            "timestamp" to System.currentTimeMillis()
        )
        return newFixedLengthResponse(Response.Status.OK, "application/json", 
            com.google.gson.Gson().toJson(healthInfo))
    }
    
    /**
     * 添加图片到缓存
     */
    fun addImage(imageId: String, bitmap: Bitmap, mimeType: String = "image/jpeg") {
        imageCache[imageId] = ImageData(bitmap = bitmap, filePath = null, mimeType = mimeType)
        Logger.d(TAG, msg = "📷 添加图片到缓存: $imageId (${bitmap.width}x${bitmap.height})")
    }
    
    /**
     * 添加文件图片到缓存
     */
    fun addImageFile(imageId: String, filePath: String, mimeType: String = "image/jpeg") {
        imageCache[imageId] = ImageData(bitmap = null, filePath = filePath, mimeType = mimeType)
        Logger.d(TAG, msg = "📁 添加文件图片到缓存: $imageId -> $filePath")
    }
    
    /**
     * 批量添加图片
     */
    fun addBatchImages(images: Map<String, Bitmap>) {
        images.forEach { (imageId, bitmap) ->
            addImage(imageId, bitmap)
        }
        Logger.d(TAG, msg = "📚 批量添加${images.size}张图片到缓存")
    }
    
    /**
     * 移除图片缓存
     */
    fun removeImage(imageId: String) {
        imageCache.remove(imageId)
        tempImages.remove(imageId)
        Logger.d(TAG, msg = "🗑️ 移除图片缓存: $imageId")
    }
    
    /**
     * 清除所有缓存
     */
    fun clearCache() {
        imageCache.clear()
        tempImages.clear()
        Logger.d(TAG, msg = "🧹 清除所有图片缓存")
    }
    
    /**
     * 获取服务器URL
     */
    fun getServerUrl(): String {
        return "http://localhost:$listeningPort"
    }
    
    /**
     * 获取图片URL
     */
    fun getImageUrl(imageId: String): String {
        return "${getServerUrl()}/image/$imageId"
    }
    
    /**
     * 获取批量图片信息URL
     */
    fun getBatchUrl(imageIds: List<String>): String {
        return "${getServerUrl()}/batch/${imageIds.joinToString(",")}"
    }
    
    // 私有辅助方法
    
    private fun getImageData(imageId: String): ImageData? {
        return imageCache[imageId] ?: loadImageFromSource(imageId)
    }
    
    private fun loadImageFromSource(imageId: String): ImageData? {
        return try {
            when {
                imageId.startsWith("material_") -> loadMaterialImage(imageId)
                imageId.startsWith("file_") -> loadFileImage(imageId)
                imageId.startsWith("temp_") -> loadTempImage(imageId)
                else -> null
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "加载图片失败: $imageId, ${e.message}")
            null
        }
    }
    
    private fun loadMaterialImage(imageId: String): ImageData? {
        // 实现素材库图片加载逻辑
        return null
    }
    
    private fun loadFileImage(imageId: String): ImageData? {
        val filePath = imageId.removePrefix("file_").replace("_", "/")
        val file = File(filePath)
        return if (file.exists()) {
            ImageData(bitmap = null, filePath = filePath, lastModified = file.lastModified())
        } else null
    }
    
    private fun loadTempImage(imageId: String): ImageData? {
        val bitmap = tempImages[imageId]
        return if (bitmap != null) {
            ImageData(bitmap = bitmap, filePath = null)
        } else null
    }
    
    private fun bitmapToInputStream(bitmap: Bitmap): InputStream {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, byteArrayOutputStream)
        return ByteArrayInputStream(byteArrayOutputStream.toByteArray())
    }
    
    private fun parseImageIdsFromJson(jsonBody: String): List<String> {
        return try {
            val gson = com.google.gson.Gson()
            val jsonObject = gson.fromJson(jsonBody, Map::class.java)
            @Suppress("UNCHECKED_CAST")
            (jsonObject["imageIds"] as? List<String>) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun formatHttpDate(timestamp: Long): String {
        val sdf = java.text.SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz", java.util.Locale.US)
        sdf.timeZone = java.util.TimeZone.getTimeZone("GMT")
        return sdf.format(java.util.Date(timestamp))
    }
}
